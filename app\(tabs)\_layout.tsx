import { StyleSheet } from 'react-native';
import { useState } from 'react';
import { Text } from '@/components/Themed';
import { BottomNavigation } from 'react-native-paper';
import ExploreScreen from './explore';
import LibraryScreen from './library';
import SearchScreen from './search';
import SettingsScreen from './settings';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { useAppTheme } from '@/constants/theme';

export default function TabLayout() {
  const { 
    colors: { 
      tabIconSelected,
      tabIconDefault,
      tabTextSelected,
      tabTextDefault,
      background,
      tabBorder,
      safeArea
    }} = useAppTheme();
  const [index, setIndex] = useState(0);
  const [routes] = useState<
    {
      key: string;
      title: string;
      materialIcon?: React.ComponentProps<typeof MaterialIcons>['name'];
      materialCommunityIcon?: React.ComponentProps<typeof MaterialCommunityIcons>['name'];
    }[]
  >([
    { key: 'explore', title: 'Explore', materialIcon: 'smart-display'},
    { key: 'search', title: 'Search', materialIcon: 'search' },
    { key: 'library', title: 'Library', materialCommunityIcon: 'playlist-play' },
    { key: 'settings', title: 'Settings', materialIcon: 'account-box' },
  ]);

  const renderScene = BottomNavigation.SceneMap({
    explore: ExploreScreen,
    search: SearchScreen,
    library: LibraryScreen,
    settings: SettingsScreen,
  });

  return (
    <SafeAreaProvider>
      <SafeAreaView style={{ flex: 1, backgroundColor: safeArea}} >
        <BottomNavigation
          theme={{colors: {secondaryContainer: undefined}}}
          navigationState={{ index, routes }}
          renderIcon={({ route, focused }) => {
            if (route.materialIcon) {
              return <MaterialIcons style={styles.tabIcon} name={route.materialIcon} color={focused ? tabIconSelected : tabIconDefault} />;
            } else if (route.materialCommunityIcon) {
              return <MaterialCommunityIcons style={styles.tabIcon} name={route.materialCommunityIcon} color={focused ? tabIconSelected : tabIconDefault} />;
            }
          }}
          barStyle={{ 
            borderTopColor: tabBorder, 
            backgroundColor: background,
            ...styles.bar
          }}
          renderLabel={({ route, focused }) => (
            <Text
              style={{
                ...styles.tabLabel,
                color: focused ? tabTextSelected: tabTextDefault
              }}
            >
              {route.title}
            </Text>
          )}
          onIndexChange={setIndex}
          renderScene={renderScene}
        />
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  tabLabel: {
    textAlign: 'center',
    fontSize: 12,
    margin: -14,
  },
  tabIcon: {
    margin: -10,
    fontSize: 26
  },
  bar: {
    borderTopWidth: 1,
    height: 58,
  }
});
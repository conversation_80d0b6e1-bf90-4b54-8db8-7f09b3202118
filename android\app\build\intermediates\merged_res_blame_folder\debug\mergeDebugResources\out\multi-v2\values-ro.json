{"logs": [{"outputFile": "com.anonymous.FormaPilates.app-mergeDebugResources-53:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,224", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1168,1291,1397,1510,1594,1699,1818,1903,1983,2074,2167,2262,2356,2456,2549,2644,2738,2829,2921,3002,3112,3220,3318,3430,3536,3640,3802,17924", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "1286,1392,1505,1589,1694,1813,1898,1978,2069,2162,2257,2351,2451,2544,2639,2733,2824,2916,2997,3107,3215,3313,3425,3531,3635,3797,3898,18001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3a82f0acae6d1f7b5cb6156d60929f6\\transformed\\exoplayer-ui-2.18.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "56,57", "startColumns": "4,4", "startOffsets": "3560,3624", "endColumns": "63,65", "endOffsets": "3619,3685"}, "to": {"startLines": "135,136", "startColumns": "4,4", "startOffsets": "10798,10862", "endColumns": "63,65", "endOffsets": "10857,10923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1142,1208,1303,1377,1437,1521,1583,1649,1707,1780,1843,1899,2018,2075,2136,2192,2266,2411,2497,2572,2661,2740,2824,2957,3039,3122,3268,3358,3438,3493,3544,3610,3683,3761,3832,3917,3988,4065,4139,4211,4317,4408,4482,4577,4675,4749,4829,4930,4983,5069,5135,5224,5314,5376,5440,5503,5577,5689,5799,5909,6014,6073,6128,6207,6293,6370", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1137,1203,1298,1372,1432,1516,1578,1644,1702,1775,1838,1894,2013,2070,2131,2187,2261,2406,2492,2567,2656,2735,2819,2952,3034,3117,3263,3353,3433,3488,3539,3605,3678,3756,3827,3912,3983,4060,4134,4206,4312,4403,4477,4572,4670,4744,4824,4925,4978,5064,5130,5219,5309,5371,5435,5498,5572,5684,5794,5904,6009,6068,6123,6202,6288,6365,6444"}, "to": {"startLines": "21,54,55,56,57,58,66,67,68,72,73,143,148,151,152,153,154,155,156,157,158,159,160,161,162,163,164,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,225,226,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "947,3973,4065,4153,4240,4336,5153,5254,5375,5720,5782,11321,11804,12033,12093,12177,12239,12305,12363,12436,12499,12555,12674,12731,12792,12848,12922,13800,13886,13961,14050,14129,14213,14346,14428,14511,14657,14747,14827,14882,14933,14999,15072,15150,15221,15306,15377,15454,15528,15600,15706,15797,15871,15966,16064,16138,16218,16319,16372,16458,16524,16613,16703,16765,16829,16892,16966,17078,17188,17298,17403,17462,17517,18006,18092,18169", "endLines": "25,54,55,56,57,58,66,67,68,72,73,143,148,151,152,153,154,155,156,157,158,159,160,161,162,163,164,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,225,226,227", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "1163,4060,4148,4235,4331,4421,5249,5370,5454,5777,5843,11411,11873,12088,12172,12234,12300,12358,12431,12494,12550,12669,12726,12787,12843,12917,13062,13881,13956,14045,14124,14208,14341,14423,14506,14652,14742,14822,14877,14928,14994,15067,15145,15216,15301,15372,15449,15523,15595,15701,15792,15866,15961,16059,16133,16213,16314,16367,16453,16519,16608,16698,16760,16824,16887,16961,17073,17183,17293,17398,17457,17512,17591,18087,18164,18243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b89802f69f7f3d2fa65274bf0a5fd9f\\transformed\\media3-session-1.4.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,235,313,416,506,594,684,777,875,971,1043,1147,1246,1339,1413,1509,1593,1682,1754,1820,1903,1990,2086", "endColumns": "74,104,77,102,89,87,89,92,97,95,71,103,98,92,73,95,83,88,71,65,82,86,95,100", "endOffsets": "125,230,308,411,501,589,679,772,870,966,1038,1142,1241,1334,1408,1504,1588,1677,1749,1815,1898,1985,2081,2182"}, "to": {"startLines": "71,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,165,166,167,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5645,5848,5953,6031,6134,6224,6312,6402,6495,6593,6689,6761,6865,6964,7057,7131,7227,7311,13067,13139,13205,13288,13375,13471", "endColumns": "74,104,77,102,89,87,89,92,97,95,71,103,98,92,73,95,83,88,71,65,82,86,95,100", "endOffsets": "5715,5948,6026,6129,6219,6307,6397,6490,6588,6684,6756,6860,6959,7052,7126,7222,7306,7395,13134,13200,13283,13370,13466,13567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2a947f51900954c22e7e3a8ad49c5aaa\\transformed\\media3-ui-1.4.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,940,1030,1119,1216,1310,1385,1451,1548,1646,1715,1778,1841,1910,2024,2137,2251,2328,2408,2477,2553,2652,2753,2819,2882,2935,2993,3041,3102,3166,3236,3301,3370,3431,3489,3555,3607,3669,3745,3821,3878", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,51,61,75,75,56,69", "endOffsets": "281,577,847,935,1025,1114,1211,1305,1380,1446,1543,1641,1710,1773,1836,1905,2019,2132,2246,2323,2403,2472,2548,2647,2748,2814,2877,2930,2988,3036,3097,3161,3231,3296,3365,3426,3484,3550,3602,3664,3740,3816,3873,3943"}, "to": {"startLines": "2,11,16,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,677,7400,7488,7578,7667,7764,7858,7933,7999,8096,8194,8263,8326,8389,8458,8572,8685,8799,8876,8956,9025,9101,9200,9301,9367,10125,10178,10236,10284,10345,10409,10479,10544,10613,10674,10732,10928,10980,11042,11118,11194,11251", "endLines": "10,15,20,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,137,138,139,140,141,142", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,51,61,75,75,56,69", "endOffsets": "376,672,942,7483,7573,7662,7759,7853,7928,7994,8091,8189,8258,8321,8384,8453,8567,8680,8794,8871,8951,9020,9096,9195,9296,9362,9425,10173,10231,10279,10340,10404,10474,10539,10608,10669,10727,10793,10975,11037,11113,11189,11246,11316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,377", "endColumns": "106,101,112,102", "endOffsets": "157,259,372,475"}, "to": {"startLines": "70,144,145,146", "startColumns": "4,4,4,4", "startOffsets": "5538,11416,11518,11631", "endColumns": "106,101,112,102", "endOffsets": "5640,11513,11626,11729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "59,60,61,62,63,64,65,236", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4426,4524,4626,4726,4825,4927,5036,18889", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "4519,4621,4721,4820,4922,5031,5148,18985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,274,345,429,497,573,657,742,832,901,985,1075,1150,1232,1311,1389,1467,1541,1626,1699,1775", "endColumns": "69,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "120,199,269,340,424,492,568,652,737,827,896,980,1070,1145,1227,1306,1384,1462,1536,1621,1694,1770,1855"}, "to": {"startLines": "53,69,147,149,150,171,172,173,220,221,222,223,228,229,230,231,232,233,234,235,237,238,239", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3903,5459,11734,11878,11949,13572,13640,13716,17596,17681,17771,17840,18248,18338,18413,18495,18574,18652,18730,18804,18990,19063,19139", "endColumns": "69,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "3968,5533,11799,11944,12028,13635,13711,13795,17676,17766,17835,17919,18333,18408,18490,18569,18647,18725,18799,18884,19058,19134,19219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a0bedbb6c5a9105d69926961a71cb91\\transformed\\media3-exoplayer-1.4.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,198,263,335,413,493,583,676", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "131,193,258,330,408,488,578,671,745"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9430,9511,9573,9638,9710,9788,9868,9958,10051", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "9506,9568,9633,9705,9783,9863,9953,10046,10120"}}]}]}
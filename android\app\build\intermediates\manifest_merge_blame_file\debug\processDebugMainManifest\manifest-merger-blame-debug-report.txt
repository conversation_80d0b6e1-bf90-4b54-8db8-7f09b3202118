1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.anonymous.FormaPilates"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:4:3-75
11-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:2:3-64
12-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:3:3-77
13-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:3:20-75
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:5:3-63
14-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:5:20-61
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:6:3-78
15-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:6:20-76
16
17    <queries>
17-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:7:3-13:13
18        <intent>
18-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:8:5-12:14
19            <action android:name="android.intent.action.VIEW" />
19-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:9:7-58
19-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:9:15-56
20
21            <category android:name="android.intent.category.BROWSABLE" />
21-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:10:7-67
21-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:10:17-65
22
23            <data android:scheme="https" />
23-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:11:7-37
23-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:11:13-35
24        </intent>
25        <!-- Query open documents -->
26        <intent>
26-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
27            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
27-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
27-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
28        </intent>
29        <intent>
29-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:8:9-12:18
30
31            <!-- Required for opening tabs if targeting API 30 -->
32            <action android:name="android.support.customtabs.action.CustomTabsService" />
32-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:13-90
32-->[host.exp.exponent:expo.modules.webbrowser:14.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\bce68187356271bdb3abb6cf30841a37\transformed\expo.modules.webbrowser-14.1.6\AndroidManifest.xml:11:21-87
33        </intent>
34    </queries>
35
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a0bedbb6c5a9105d69926961a71cb91\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
36-->[androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a0bedbb6c5a9105d69926961a71cb91\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:22-76
37
38    <permission
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.anonymous.FormaPilates.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.anonymous.FormaPilates.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
43
44    <application
44-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:14:3-30:17
45        android:name="com.anonymous.FormaPilates.MainApplication"
45-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:14:16-47
46        android:allowBackup="true"
46-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:14:162-188
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00054a46db3025f014b8174c079d22f2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:icon="@mipmap/ic_launcher"
50-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:14:81-115
51        android:label="@string/app_name"
51-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:14:48-80
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:14:116-161
53        android:supportsRtl="true"
53-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:14:221-247
54        android:theme="@style/AppTheme"
54-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:14:189-220
55        android:usesCleartextTraffic="true" >
55-->D:\Work\Devscribed\FormaPilates\android\app\src\debug\AndroidManifest.xml:6:18-53
56        <meta-data
56-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:15:5-83
57            android:name="expo.modules.updates.ENABLED"
57-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:15:16-59
58            android:value="false" />
58-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:15:60-81
59        <meta-data
59-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:16:5-105
60            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
60-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:16:16-80
61            android:value="ALWAYS" />
61-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:16:81-103
62        <meta-data
62-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:17:5-99
63            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
63-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:17:16-79
64            android:value="0" />
64-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:17:80-97
65
66        <activity
66-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:18:5-29:16
67            android:name="com.anonymous.FormaPilates.MainActivity"
67-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:18:15-43
68            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
68-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:18:44-134
69            android:exported="true"
69-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:18:256-279
70            android:launchMode="singleTask"
70-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:18:135-166
71            android:screenOrientation="portrait"
71-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:18:280-316
72            android:theme="@style/Theme.App.SplashScreen"
72-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:18:210-255
73            android:windowSoftInputMode="adjustResize" >
73-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:18:167-209
74            <intent-filter>
74-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:19:7-22:23
75                <action android:name="android.intent.action.MAIN" />
75-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:20:9-60
75-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:20:17-58
76
77                <category android:name="android.intent.category.LAUNCHER" />
77-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:21:9-68
77-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:21:19-66
78            </intent-filter>
79            <intent-filter>
79-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:23:7-28:23
80                <action android:name="android.intent.action.VIEW" />
80-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:9:7-58
80-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:9:15-56
81
82                <category android:name="android.intent.category.DEFAULT" />
82-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:25:9-67
82-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:25:19-65
83                <category android:name="android.intent.category.BROWSABLE" />
83-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:10:7-67
83-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:10:17-65
84
85                <data android:scheme="formapilates" />
85-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:11:7-37
85-->D:\Work\Devscribed\FormaPilates\android\app\src\main\AndroidManifest.xml:11:13-35
86            </intent-filter>
87        </activity>
88
89        <meta-data
89-->[:expo-modules-core] D:\Work\Devscribed\FormaPilates\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
90            android:name="org.unimodules.core.AppLoader#react-native-headless"
90-->[:expo-modules-core] D:\Work\Devscribed\FormaPilates\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
91            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
91-->[:expo-modules-core] D:\Work\Devscribed\FormaPilates\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
92        <meta-data
92-->[:expo-modules-core] D:\Work\Devscribed\FormaPilates\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
93            android:name="com.facebook.soloader.enabled"
93-->[:expo-modules-core] D:\Work\Devscribed\FormaPilates\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
94            android:value="true" />
94-->[:expo-modules-core] D:\Work\Devscribed\FormaPilates\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
95
96        <activity
96-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
97            android:name="com.facebook.react.devsupport.DevSettingsActivity"
97-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
98            android:exported="false" />
98-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5f3eb598334922b3ec7ac664aab84b4\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
99
100        <provider
100-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
101            android:name="expo.modules.filesystem.FileSystemFileProvider"
101-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
102            android:authorities="com.anonymous.FormaPilates.FileSystemFileProvider"
102-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
103            android:exported="false"
103-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
104            android:grantUriPermissions="true" >
104-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
105            <meta-data
105-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:70
106                android:name="android.support.FILE_PROVIDER_PATHS"
106-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-67
107                android:resource="@xml/file_system_provider_paths" />
107-->[:expo-file-system] D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-67
108        </provider>
109        <provider
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
110            android:name="androidx.startup.InitializationProvider"
110-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
111            android:authorities="com.anonymous.FormaPilates.androidx-startup"
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
112            android:exported="false" >
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
113            <meta-data
113-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.emoji2.text.EmojiCompatInitializer"
114-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
115                android:value="androidx.startup" />
115-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb1ead32b7ca366206954fd10d393c9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
116            <meta-data
116-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
117-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
118                android:value="androidx.startup" />
118-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\56e3979bc4d1abd6cc4a6649c40e004c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
119            <meta-data
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
120                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
121                android:value="androidx.startup" />
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
122        </provider>
123
124        <receiver
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
125            android:name="androidx.profileinstaller.ProfileInstallReceiver"
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
126            android:directBootAware="false"
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
127            android:enabled="true"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
128            android:exported="true"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
129            android:permission="android.permission.DUMP" >
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
131                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
134                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
137                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
138            </intent-filter>
139            <intent-filter>
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
140                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35150bde59e21bbb1ef13e07cb970323\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
141            </intent-filter>
142        </receiver>
143    </application>
144
145</manifest>

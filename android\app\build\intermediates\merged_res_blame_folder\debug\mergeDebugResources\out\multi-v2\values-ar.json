{"logs": [{"outputFile": "com.anonymous.FormaPilates.app-mergeDebugResources-53:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "79,153,154,155", "startColumns": "4,4,4,4", "startOffsets": "6001,11805,11903,12011", "endColumns": "99,97,107,101", "endOffsets": "6096,11898,12006,12108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "27,63,64,65,66,67,75,76,77,81,82,152,157,160,162,163,164,165,166,167,168,169,170,171,172,173,174,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,235,236,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1409,4497,4575,4651,4735,4827,5622,5723,5842,6180,6239,11714,12185,12400,12534,12634,12697,12762,12823,12891,12953,13011,13125,13185,13246,13303,13376,14203,14284,14376,14483,14581,14661,14809,14890,14971,15099,15188,15264,15317,15371,15437,15515,15595,15666,15748,15820,15894,15967,16037,16146,16237,16308,16398,16493,16567,16650,16743,16792,16873,16942,17028,17113,17175,17239,17302,17371,17480,17590,17687,17787,17844,17902,18391,18470,18545", "endLines": "34,63,64,65,66,67,75,76,77,81,82,152,157,160,162,163,164,165,166,167,168,169,170,171,172,173,174,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,235,236,237", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "1769,4570,4646,4730,4822,4905,5718,5837,5914,6234,6297,11800,12249,12462,12629,12692,12757,12818,12886,12948,13006,13120,13180,13241,13298,13371,13494,14279,14371,14478,14576,14656,14804,14885,14966,15094,15183,15259,15312,15366,15432,15510,15590,15661,15743,15815,15889,15962,16032,16141,16232,16303,16393,16488,16562,16645,16738,16787,16868,16937,17023,17108,17170,17234,17297,17366,17475,17585,17682,17782,17839,17897,17977,18465,18540,18616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a0bedbb6c5a9105d69926961a71cb91\\transformed\\media3-exoplayer-1.4.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "124,125,126,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9826,9891,9950,10017,10079,10161,10242,10343,10438", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "9886,9945,10012,10074,10156,10237,10338,10433,10517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3a82f0acae6d1f7b5cb6156d60929f6\\transformed\\exoplayer-ui-2.18.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "62,63", "startColumns": "4,4", "startOffsets": "3944,4006", "endColumns": "61,65", "endOffsets": "4001,4067"}, "to": {"startLines": "144,145", "startColumns": "4,4", "startOffsets": "11211,11273", "endColumns": "61,65", "endOffsets": "11268,11334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,206,278,346,424,491,561,639,718,799,887,965,1045,1129,1203,1281,1358,1433,1512,1584,1668,1738,1824,1893", "endColumns": "68,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "119,201,273,341,419,486,556,634,713,794,882,960,1040,1124,1198,1276,1353,1428,1507,1579,1663,1733,1819,1888,1966"}, "to": {"startLines": "62,78,156,158,159,161,181,182,183,230,231,232,233,238,239,240,241,242,243,244,245,247,248,249,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4428,5919,12113,12254,12322,12467,13976,14046,14124,17982,18063,18151,18229,18621,18705,18779,18857,18934,19009,19088,19160,19345,19415,19501,19570", "endColumns": "68,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "4492,5996,12180,12317,12395,12529,14041,14119,14198,18058,18146,18224,18304,18700,18774,18852,18929,19004,19083,19155,19239,19410,19496,19565,19643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,234", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1774,1882,1986,2093,2175,2276,2390,2470,2549,2640,2733,2825,2919,3019,3112,3207,3300,3391,3485,3564,3669,3767,3865,3973,4073,4176,4331,18309", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "1877,1981,2088,2170,2271,2385,2465,2544,2635,2728,2820,2914,3014,3107,3202,3295,3386,3480,3559,3664,3762,3860,3968,4068,4171,4326,4423,18386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "68,69,70,71,72,73,74,246", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4910,5003,5105,5200,5303,5406,5508,19244", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "4998,5100,5195,5298,5401,5503,5617,19340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2a947f51900954c22e7e3a8ad49c5aaa\\transformed\\media3-ui-1.4.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2428,2537,2652,2718,2801,2873,2945,3037,3128,3192,3255,3308,3379,3434,3495,3553,3627,3691,3755,3815,3880,3944,3996,4053,4124,4195,4251", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,51,56,70,70,55,67", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2423,2532,2647,2713,2796,2868,2940,3032,3123,3187,3250,3303,3374,3429,3490,3548,3622,3686,3750,3810,3875,3939,3991,4048,4119,4190,4246,4314"}, "to": {"startLines": "2,11,19,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,444,944,7885,7964,8042,8118,8212,8304,8378,8443,8535,8625,8695,8759,8822,8891,8999,9108,9223,9289,9372,9444,9516,9608,9699,9763,10522,10575,10646,10701,10762,10820,10894,10958,11022,11082,11147,11339,11391,11448,11519,11590,11646", "endLines": "10,18,26,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,133,134,135,136,137,138,139,140,141,142,143,146,147,148,149,150,151", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,51,56,70,70,55,67", "endOffsets": "439,939,1404,7959,8037,8113,8207,8299,8373,8438,8530,8620,8690,8754,8817,8886,8994,9103,9218,9284,9367,9439,9511,9603,9694,9758,9821,10570,10641,10696,10757,10815,10889,10953,11017,11077,11142,11206,11386,11443,11514,11585,11641,11709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b89802f69f7f3d2fa65274bf0a5fd9f\\transformed\\media3-session-1.4.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,233,314,432,527,612,715,798,906,1003,1071,1181,1274,1362,1440,1541,1616,1717,1790,1857,1929,2004,2097", "endColumns": "78,98,80,117,94,84,102,82,107,96,67,109,92,87,77,100,74,100,72,66,71,74,92,96", "endOffsets": "129,228,309,427,522,607,710,793,901,998,1066,1176,1269,1357,1435,1536,1611,1712,1785,1852,1924,1999,2092,2189"}, "to": {"startLines": "80,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,175,176,177,178,179,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6101,6302,6401,6482,6600,6695,6780,6883,6966,7074,7171,7239,7349,7442,7530,7608,7709,7784,13499,13572,13639,13711,13786,13879", "endColumns": "78,98,80,117,94,84,102,82,107,96,67,109,92,87,77,100,74,100,72,66,71,74,92,96", "endOffsets": "6175,6396,6477,6595,6690,6775,6878,6961,7069,7166,7234,7344,7437,7525,7603,7704,7779,7880,13567,13634,13706,13781,13874,13971"}}]}]}
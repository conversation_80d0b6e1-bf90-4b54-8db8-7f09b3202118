{"logs": [{"outputFile": "com.anonymous.FormaPilates.app-mergeDebugResources-53:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3a82f0acae6d1f7b5cb6156d60929f6\\transformed\\exoplayer-ui-2.18.1\\res\\values-af\\values-af.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3322,3386", "endColumns": "63,65", "endOffsets": "3381,3447"}, "to": {"startLines": "132,133", "startColumns": "4,4", "startOffsets": "10333,10397", "endColumns": "63,65", "endOffsets": "10392,10458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,283,352,436,505,575,652,730,813,892,964,1043,1122,1196,1280,1364,1443,1513,1583,1665,1740,1816,1888", "endColumns": "72,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "123,206,278,347,431,500,570,647,725,808,887,959,1038,1117,1191,1275,1359,1438,1508,1578,1660,1735,1811,1883,1957"}, "to": {"startLines": "50,66,144,146,147,149,169,170,171,218,219,220,221,226,227,228,229,230,231,232,233,235,236,237,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3630,5152,11245,11383,11452,11599,13094,13164,13241,16994,17077,17156,17228,17620,17699,17773,17857,17941,18020,18090,18160,18343,18418,18494,18566", "endColumns": "72,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "3698,5230,11312,11447,11531,11663,13159,13236,13314,17072,17151,17223,17302,17694,17768,17852,17936,18015,18085,18155,18237,18413,18489,18561,18635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "958,1066,1162,1268,1353,1456,1574,1651,1727,1818,1911,2006,2100,2199,2292,2387,2486,2581,2675,2756,2863,2968,3065,3173,3276,3378,3532,17307", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "1061,1157,1263,1348,1451,1569,1646,1722,1813,1906,2001,2095,2194,2287,2382,2481,2576,2670,2751,2858,2963,3060,3168,3271,3373,3527,3625,17383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b89802f69f7f3d2fa65274bf0a5fd9f\\transformed\\media3-session-1.4.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,228,301,401,494,573,669,749,846,938,1003,1105,1194,1295,1371,1465,1545,1639,1712,1779,1859,1942,2037", "endColumns": "72,99,72,99,92,78,95,79,96,91,64,101,88,100,75,93,79,93,72,66,79,82,94,96", "endOffsets": "123,223,296,396,489,568,664,744,841,933,998,1100,1189,1290,1366,1460,1540,1634,1707,1774,1854,1937,2032,2129"}, "to": {"startLines": "68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,163,164,165,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5340,5537,5637,5710,5810,5903,5982,6078,6158,6255,6347,6412,6514,6603,6704,6780,6874,6954,12599,12672,12739,12819,12902,12997", "endColumns": "72,99,72,99,92,78,95,79,96,91,64,101,88,100,75,93,79,93,72,66,79,82,94,96", "endOffsets": "5408,5632,5705,5805,5898,5977,6073,6153,6250,6342,6407,6509,6598,6699,6775,6869,6949,7043,12667,12734,12814,12897,12992,13089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2a947f51900954c22e7e3a8ad49c5aaa\\transformed\\media3-ui-1.4.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,500,689,776,864,944,1031,1117,1188,1255,1353,1446,1516,1580,1642,1711,1826,1940,2053,2125,2207,2281,2347,2434,2522,2585,2650,2703,2761,2809,2870,2935,3007,3072,3140,3198,3256,3322,3374,3433,3506,3579,3634", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,114,113,112,71,81,73,65,86,87,62,64,52,57,47,60,64,71,64,67,57,57,65,51,58,72,72,54,66", "endOffsets": "281,495,684,771,859,939,1026,1112,1183,1250,1348,1441,1511,1575,1637,1706,1821,1935,2048,2120,2202,2276,2342,2429,2517,2580,2645,2698,2756,2804,2865,2930,3002,3067,3135,3193,3251,3317,3369,3428,3501,3574,3629,3696"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,595,7048,7135,7223,7303,7390,7476,7547,7614,7712,7805,7875,7939,8001,8070,8185,8299,8412,8484,8566,8640,8706,8793,8881,8944,9661,9714,9772,9820,9881,9946,10018,10083,10151,10209,10267,10463,10515,10574,10647,10720,10775", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,134,135,136,137,138,139", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,114,113,112,71,81,73,65,86,87,62,64,52,57,47,60,64,71,64,67,57,57,65,51,58,72,72,54,66", "endOffsets": "376,590,779,7130,7218,7298,7385,7471,7542,7609,7707,7800,7870,7934,7996,8065,8180,8294,8407,8479,8561,8635,8701,8788,8876,8939,9004,9709,9767,9815,9876,9941,10013,10078,10146,10204,10262,10328,10510,10569,10642,10715,10770,10837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "56,57,58,59,60,61,62,234", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4125,4223,4325,4423,4521,4628,4737,18242", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "4218,4320,4418,4516,4623,4732,4852,18338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,608,696,796,910,991,1051,1115,1203,1269,1332,1418,1480,1541,1599,1665,1728,1783,1901,1958,2020,2075,2144,2263,2351,2426,2519,2604,2687,2826,2909,2990,3118,3205,3282,3340,3391,3457,3526,3602,3673,3749,3823,3902,3975,4046,4149,4236,4307,4396,4486,4558,4633,4720,4771,4850,4917,4998,5082,5144,5208,5271,5341,5445,5548,5644,5744,5806,5861,5938,6021,6097", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,77,94,87,99,113,80,59,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76,82,75,72", "endOffsets": "269,350,430,508,603,691,791,905,986,1046,1110,1198,1264,1327,1413,1475,1536,1594,1660,1723,1778,1896,1953,2015,2070,2139,2258,2346,2421,2514,2599,2682,2821,2904,2985,3113,3200,3277,3335,3386,3452,3521,3597,3668,3744,3818,3897,3970,4041,4144,4231,4302,4391,4481,4553,4628,4715,4766,4845,4912,4993,5077,5139,5203,5266,5336,5440,5543,5639,5739,5801,5856,5933,6016,6092,6165"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,69,70,140,145,148,150,151,152,153,154,155,156,157,158,159,160,161,162,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,223,224,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,3703,3784,3864,3942,4037,4857,4957,5071,5413,5473,10842,11317,11536,11668,11754,11816,11877,11935,12001,12064,12119,12237,12294,12356,12411,12480,13319,13407,13482,13575,13660,13743,13882,13965,14046,14174,14261,14338,14396,14447,14513,14582,14658,14729,14805,14879,14958,15031,15102,15205,15292,15363,15452,15542,15614,15689,15776,15827,15906,15973,16054,16138,16200,16264,16327,16397,16501,16604,16700,16800,16862,16917,17388,17471,17547", "endLines": "22,51,52,53,54,55,63,64,65,69,70,140,145,148,150,151,152,153,154,155,156,157,158,159,160,161,162,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,223,224,225", "endColumns": "12,80,79,77,94,87,99,113,80,59,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76,82,75,72", "endOffsets": "953,3779,3859,3937,4032,4120,4952,5066,5147,5468,5532,10925,11378,11594,11749,11811,11872,11930,11996,12059,12114,12232,12289,12351,12406,12475,12594,13402,13477,13570,13655,13738,13877,13960,14041,14169,14256,14333,14391,14442,14508,14577,14653,14724,14800,14874,14953,15026,15097,15200,15287,15358,15447,15537,15609,15684,15771,15822,15901,15968,16049,16133,16195,16259,16322,16392,16496,16599,16695,16795,16857,16912,16989,17466,17542,17615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,99", "endOffsets": "155,257,370,470"}, "to": {"startLines": "67,141,142,143", "startColumns": "4,4,4,4", "startOffsets": "5235,10930,11032,11145", "endColumns": "104,101,112,99", "endOffsets": "5335,11027,11140,11240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a0bedbb6c5a9105d69926961a71cb91\\transformed\\media3-exoplayer-1.4.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,184,250,317,392,462,551,635", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "119,179,245,312,387,457,546,630,702"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9009,9078,9138,9204,9271,9346,9416,9505,9589", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "9073,9133,9199,9266,9341,9411,9500,9584,9656"}}]}]}
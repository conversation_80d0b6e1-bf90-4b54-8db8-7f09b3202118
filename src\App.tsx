import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Provider} from 'react-redux';
import {store} from '../store';
import {SafeAreaProvider} from 'react-native-safe-area-context';

// Import screens
import ExploreScreen from './screens/ExploreScreen';
import LibraryScreen from './screens/LibraryScreen';
import SearchScreen from './screens/SearchScreen';
import SettingsScreen from './screens/SettingsScreen';

const Tab = createBottomTabNavigator();

function App(): React.JSX.Element {
  return (
    <Provider store={store}>
      <SafeAreaProvider>
        <NavigationContainer>
          <Tab.Navigator
            screenOptions={{
              headerShown: false,
              tabBarStyle: {
                backgroundColor: '#fff',
              },
            }}>
            <Tab.Screen 
              name="Explore" 
              component={ExploreScreen}
              options={{
                tabBarLabel: 'Explore',
              }}
            />
            <Tab.Screen 
              name="Search" 
              component={SearchScreen}
              options={{
                tabBarLabel: 'Search',
              }}
            />
            <Tab.Screen 
              name="Library" 
              component={LibraryScreen}
              options={{
                tabBarLabel: 'Library',
              }}
            />
            <Tab.Screen 
              name="Settings" 
              component={SettingsScreen}
              options={{
                tabBarLabel: 'Settings',
              }}
            />
          </Tab.Navigator>
        </NavigationContainer>
      </SafeAreaProvider>
    </Provider>
  );
}

export default App;

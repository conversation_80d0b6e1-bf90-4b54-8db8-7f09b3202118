{"name": "FormaPilates", "slug": "FormaPilates", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "formapilates", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.anonymous.FormaPilates"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}, "sdkVersion": "53.0.0", "platforms": ["ios", "android", "web"], "extra": {"router": {}}}
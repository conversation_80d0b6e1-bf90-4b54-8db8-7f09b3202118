{"logs": [{"outputFile": "com.anonymous.FormaPilates.app-mergeDebugResources-53:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3a82f0acae6d1f7b5cb6156d60929f6\\transformed\\exoplayer-ui-2.18.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3390,3454", "endColumns": "63,65", "endOffsets": "3449,3515"}, "to": {"startLines": "130,131", "startColumns": "4,4", "startOffsets": "10450,10514", "endColumns": "63,65", "endOffsets": "10509,10575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b89802f69f7f3d2fa65274bf0a5fd9f\\transformed\\media3-session-1.4.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,240,313,418,517,598,677,772,858,941,1008,1114,1204,1305,1392,1490,1571,1666,1736,1804,1887,1974,2071", "endColumns": "79,104,72,104,98,80,78,94,85,82,66,105,89,100,86,97,80,94,69,67,82,86,96,99", "endOffsets": "130,235,308,413,512,593,672,767,853,936,1003,1109,1199,1300,1387,1485,1566,1661,1731,1799,1882,1969,2066,2166"}, "to": {"startLines": "66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5307,5512,5617,5690,5795,5894,5975,6054,6149,6235,6318,6385,6491,6581,6682,6769,6867,6948,12520,12590,12658,12741,12828,12925", "endColumns": "79,104,72,104,98,80,78,94,85,82,66,105,89,100,86,97,80,94,69,67,82,86,96,99", "endOffsets": "5382,5612,5685,5790,5889,5970,6049,6144,6230,6313,6380,6486,6576,6677,6764,6862,6943,7038,12585,12653,12736,12823,12920,13020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "967,1078,1186,1299,1387,1493,1608,1688,1765,1856,1949,2044,2138,2238,2331,2426,2520,2611,2702,2786,2895,3005,3106,3216,3334,3442,3605,16903", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "1073,1181,1294,1382,1488,1603,1683,1760,1851,1944,2039,2133,2233,2326,2421,2515,2606,2697,2781,2890,3000,3101,3211,3329,3437,3600,3702,16983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "65,139,140,141", "startColumns": "4,4,4,4", "startOffsets": "5204,11077,11182,11293", "endColumns": "102,104,110,104", "endOffsets": "5302,11177,11288,11393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "55,56,57,58,59,60,61,213", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4144,4241,4343,4444,4541,4648,4756,17228", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "4236,4338,4439,4536,4643,4751,4873,17324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2a947f51900954c22e7e3a8ad49c5aaa\\transformed\\media3-ui-1.4.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,497,692,783,874,954,1039,1130,1208,1274,1375,1478,1545,1610,1672,1743,1861,1981,2101,2170,2257,2331,2411,2502,2593,2658,2722,2775,2833,2881,2942,3007,3069,3134,3202,3266,3324,3390,3442,3504,3583,3662,3719", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,51,61,78,78,56,68", "endOffsets": "280,492,687,778,869,949,1034,1125,1203,1269,1370,1473,1540,1605,1667,1738,1856,1976,2096,2165,2252,2326,2406,2497,2588,2653,2717,2770,2828,2876,2937,3002,3064,3129,3197,3261,3319,3385,3437,3499,3578,3657,3714,3783"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,592,7043,7134,7225,7305,7390,7481,7559,7625,7726,7829,7896,7961,8023,8094,8212,8332,8452,8521,8608,8682,8762,8853,8944,9009,9782,9835,9893,9941,10002,10067,10129,10194,10262,10326,10384,10580,10632,10694,10773,10852,10909", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,132,133,134,135,136,137", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,51,61,78,78,56,68", "endOffsets": "375,587,782,7129,7220,7300,7385,7476,7554,7620,7721,7824,7891,7956,8018,8089,8207,8327,8447,8516,8603,8677,8757,8848,8939,9004,9068,9830,9888,9936,9997,10062,10124,10189,10257,10321,10379,10445,10627,10689,10768,10847,10904,10973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a0bedbb6c5a9105d69926961a71cb91\\transformed\\media3-exoplayer-1.4.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,196,264,330,410,488,588,686", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "127,191,259,325,405,483,583,681,759"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9073,9150,9214,9282,9348,9428,9506,9606,9704", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "9145,9209,9277,9343,9423,9501,9601,9699,9777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,280,364,444,530,627,717,822,958,1043,1103,1168,1267,1335,1394,1483,1551,1618,1681,1756,1824,1878,1998,2056,2118,2172,2247,2389,2479,2557,2651,2734,2819,2964,3048,3131,3277,3373,3450,3508,3559,3625,3699,3777,3848,3934,4008,4087,4160,4232,4348,4452,4525,4624,4724,4798,4873,4980,5032,5121,5188,5279,5373,5435,5499,5562,5632,5751,5856,5965,6065,6127,6182,6267,6354,6432", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "275,359,439,525,622,712,817,953,1038,1098,1163,1262,1330,1389,1478,1546,1613,1676,1751,1819,1873,1993,2051,2113,2167,2242,2384,2474,2552,2646,2729,2814,2959,3043,3126,3272,3368,3445,3503,3554,3620,3694,3772,3843,3929,4003,4082,4155,4227,4343,4447,4520,4619,4719,4793,4868,4975,5027,5116,5183,5274,5368,5430,5494,5557,5627,5746,5851,5960,6060,6122,6177,6262,6349,6427,6502"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,67,68,138,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,210,211,212", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3707,3791,3871,3957,4054,4878,4983,5119,5387,5447,10978,11398,11466,11525,11614,11682,11749,11812,11887,11955,12009,12129,12187,12249,12303,12378,13025,13115,13193,13287,13370,13455,13600,13684,13767,13913,14009,14086,14144,14195,14261,14335,14413,14484,14570,14644,14723,14796,14868,14984,15088,15161,15260,15360,15434,15509,15616,15668,15757,15824,15915,16009,16071,16135,16198,16268,16387,16492,16601,16701,16763,16818,16988,17075,17153", "endLines": "22,50,51,52,53,54,62,63,64,67,68,138,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,210,211,212", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "962,3786,3866,3952,4049,4139,4978,5114,5199,5442,5507,11072,11461,11520,11609,11677,11744,11807,11882,11950,12004,12124,12182,12244,12298,12373,12515,13110,13188,13282,13365,13450,13595,13679,13762,13908,14004,14081,14139,14190,14256,14330,14408,14479,14565,14639,14718,14791,14863,14979,15083,15156,15255,15355,15429,15504,15611,15663,15752,15819,15910,16004,16066,16130,16193,16263,16382,16487,16596,16696,16758,16813,16898,17070,17148,17223"}}]}]}
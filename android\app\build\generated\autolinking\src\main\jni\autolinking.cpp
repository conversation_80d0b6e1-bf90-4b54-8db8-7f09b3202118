/**
 * This code was generated by [React Native](https://www.npmjs.com/package/@react-native/gradle-plugin).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 */

#include "autolinking.h"
#include <RNCSlider.h>
#include <react/renderer/components/RNCSlider/ComponentDescriptors.h>
#include <rnreanimated.h>
#include <safeareacontext.h>
#include <react/renderer/components/safeareacontext/ComponentDescriptors.h>
#include <rnscreens.h>
#include <react/renderer/components/rnscreens/ComponentDescriptors.h>
#include <RNEdgeToEdge.h>

namespace facebook {
namespace react {

std::shared_ptr<TurboModule> autolinking_ModuleProvider(const std::string moduleName, const JavaTurboModule::InitParams &params) {
auto module_RNCSlider = RNCSlider_ModuleProvider(moduleName, params);
if (module_RNCSlider != nullptr) {
return module_RNCSlider;
}
auto module_rnreanimated = rnreanimated_ModuleProvider(moduleName, params);
if (module_rnreanimated != nullptr) {
return module_rnreanimated;
}
auto module_safeareacontext = safeareacontext_ModuleProvider(moduleName, params);
if (module_safeareacontext != nullptr) {
return module_safeareacontext;
}
auto module_rnscreens = rnscreens_ModuleProvider(moduleName, params);
if (module_rnscreens != nullptr) {
return module_rnscreens;
}
auto module_RNEdgeToEdge = RNEdgeToEdge_ModuleProvider(moduleName, params);
if (module_RNEdgeToEdge != nullptr) {
return module_RNEdgeToEdge;
}
  return nullptr;
}

std::shared_ptr<TurboModule> autolinking_cxxModuleProvider(const std::string moduleName, const std::shared_ptr<CallInvoker>& jsInvoker) {

  return nullptr;
}

void autolinking_registerProviders(std::shared_ptr<ComponentDescriptorProviderRegistry const> providerRegistry) {
providerRegistry->add(concreteComponentDescriptorProvider<RNCSliderComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCSafeAreaProviderComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCSafeAreaViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSFullWindowOverlayComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenContainerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenNavigationContainerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenStackHeaderConfigComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenStackHeaderSubviewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenStackComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSSearchBarComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenFooterComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenContentWrapperComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSModalScreenComponentDescriptor>());
  return;
}

} // namespace react
} // namespace facebook
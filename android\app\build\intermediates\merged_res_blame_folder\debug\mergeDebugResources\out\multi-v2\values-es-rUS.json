{"logs": [{"outputFile": "com.anonymous.FormaPilates.app-mergeDebugResources-53:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,631,727,829,957,1038,1100,1165,1260,1330,1393,1486,1550,1622,1685,1759,1823,1879,1997,2055,2117,2173,2253,2387,2476,2552,2650,2731,2812,2953,3034,3114,3265,3355,3432,3488,3544,3610,3689,3771,3842,3931,4004,4081,4151,4228,4334,4423,4497,4591,4693,4765,4846,4950,5003,5088,5155,5248,5337,5399,5463,5526,5594,5705,5816,5918,6023,6083,6143,6226,6309,6385", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,79,85,104,95,101,127,80,61,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82,82,75,76", "endOffsets": "273,355,435,521,626,722,824,952,1033,1095,1160,1255,1325,1388,1481,1545,1617,1680,1754,1818,1874,1992,2050,2112,2168,2248,2382,2471,2547,2645,2726,2807,2948,3029,3109,3260,3350,3427,3483,3539,3605,3684,3766,3837,3926,3999,4076,4146,4223,4329,4418,4492,4586,4688,4760,4841,4945,4998,5083,5150,5243,5332,5394,5458,5521,5589,5700,5811,5913,6018,6078,6138,6221,6304,6380,6457"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,67,68,138,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,210,211,212", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "757,3651,3733,3813,3899,4004,4832,4934,5062,5327,5389,10935,11353,11423,11486,11579,11643,11715,11778,11852,11916,11972,12090,12148,12210,12266,12346,12974,13063,13139,13237,13318,13399,13540,13621,13701,13852,13942,14019,14075,14131,14197,14276,14358,14429,14518,14591,14668,14738,14815,14921,15010,15084,15178,15280,15352,15433,15537,15590,15675,15742,15835,15924,15986,16050,16113,16181,16292,16403,16505,16610,16670,16730,16896,16979,17055", "endLines": "22,50,51,52,53,54,62,63,64,67,68,138,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,210,211,212", "endColumns": "12,81,79,85,104,95,101,127,80,61,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,75,97,80,80,140,80,79,150,89,76,55,55,65,78,81,70,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82,82,75,76", "endOffsets": "930,3728,3808,3894,3999,4095,4929,5057,5138,5384,5449,11025,11418,11481,11574,11638,11710,11773,11847,11911,11967,12085,12143,12205,12261,12341,12475,13058,13134,13232,13313,13394,13535,13616,13696,13847,13937,14014,14070,14126,14192,14271,14353,14424,14513,14586,14663,14733,14810,14916,15005,15079,15173,15275,15347,15428,15532,15585,15670,15737,15830,15919,15981,16045,16108,16176,16287,16398,16500,16605,16665,16725,16808,16974,17050,17127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1055,1164,1272,1357,1459,1575,1660,1740,1831,1924,2019,2113,2212,2305,2404,2500,2591,2682,2764,2871,2970,3069,3177,3285,3392,3551,16813", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "1050,1159,1267,1352,1454,1570,1655,1735,1826,1919,2014,2108,2207,2300,2399,2495,2586,2677,2759,2866,2965,3064,3172,3280,3387,3546,3646,16891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b89802f69f7f3d2fa65274bf0a5fd9f\\transformed\\media3-session-1.4.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,221,302,424,529,618,708,811,921,1025,1094,1211,1305,1404,1480,1574,1660,1752,1821,1893,1970,2047,2145", "endColumns": "74,90,80,121,104,88,89,102,109,103,68,116,93,98,75,93,85,91,68,71,76,76,97,100", "endOffsets": "125,216,297,419,524,613,703,806,916,1020,1089,1206,1300,1399,1475,1569,1655,1747,1816,1888,1965,2042,2140,2241"}, "to": {"startLines": "66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5252,5454,5545,5626,5748,5853,5942,6032,6135,6245,6349,6418,6535,6629,6728,6804,6898,6984,12480,12549,12621,12698,12775,12873", "endColumns": "74,90,80,121,104,88,89,102,109,103,68,116,93,98,75,93,85,91,68,71,76,76,97,100", "endOffsets": "5322,5540,5621,5743,5848,5937,6027,6130,6240,6344,6413,6530,6624,6723,6799,6893,6979,7071,12544,12616,12693,12770,12868,12969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2a947f51900954c22e7e3a8ad49c5aaa\\transformed\\media3-ui-1.4.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,475,662,748,835,908,1004,1100,1180,1248,1347,1446,1512,1581,1647,1718,1813,1908,2003,2074,2158,2234,2314,2412,2511,2577,2641,2694,2752,2800,2861,2926,2988,3054,3126,3190,3251,3317,3370,3435,3514,3593,3651", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,52,64,78,78,57,69", "endOffsets": "280,470,657,743,830,903,999,1095,1175,1243,1342,1441,1507,1576,1642,1713,1808,1903,1998,2069,2153,2229,2309,2407,2506,2572,2636,2689,2747,2795,2856,2921,2983,3049,3121,3185,3246,3312,3365,3430,3509,3588,3646,3716"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,570,7076,7162,7249,7322,7418,7514,7594,7662,7761,7860,7926,7995,8061,8132,8227,8322,8417,8488,8572,8648,8728,8826,8925,8991,9724,9777,9835,9883,9944,10009,10071,10137,10209,10273,10334,10531,10584,10649,10728,10807,10865", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,132,133,134,135,136,137", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,52,64,78,78,57,69", "endOffsets": "375,565,752,7157,7244,7317,7413,7509,7589,7657,7756,7855,7921,7990,8056,8127,8222,8317,8412,8483,8567,8643,8723,8821,8920,8986,9050,9772,9830,9878,9939,10004,10066,10132,10204,10268,10329,10395,10579,10644,10723,10802,10860,10930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "55,56,57,58,59,60,61,213", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4100,4199,4301,4401,4499,4606,4712,17132", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "4194,4296,4396,4494,4601,4707,4827,17228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,266,381", "endColumns": "108,101,114,105", "endOffsets": "159,261,376,482"}, "to": {"startLines": "65,139,140,141", "startColumns": "4,4,4,4", "startOffsets": "5143,11030,11132,11247", "endColumns": "108,101,114,105", "endOffsets": "5247,11127,11242,11348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a0bedbb6c5a9105d69926961a71cb91\\transformed\\media3-exoplayer-1.4.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,193,258,327,404,478,567,655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "125,188,253,322,399,473,562,650,719"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9055,9130,9193,9258,9327,9404,9478,9567,9655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "9125,9188,9253,9322,9399,9473,9562,9650,9719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3a82f0acae6d1f7b5cb6156d60929f6\\transformed\\exoplayer-ui-2.18.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3317,3382", "endColumns": "64,65", "endOffsets": "3377,3443"}, "to": {"startLines": "130,131", "startColumns": "4,4", "startOffsets": "10400,10465", "endColumns": "64,65", "endOffsets": "10460,10526"}}]}]}
import { MD3LightTheme, MD3DarkTheme, useTheme, MD3Theme } from 'react-native-paper';
import { MD3Colors } from 'react-native-paper/lib/typescript/types';

const black = '#000';
const white = '#fff';

interface Colors {
  text: string;
  background: string;
  primary: string;
  secondary: string;
  tabIconDefault: string;
  tabTextDefault: string;
  tabIconSelected: string;
  tabTextSelected: string;
  tabBorder: string;
  safeArea: string;
}

export const Light: Colors = {
  text: black,
  background: white,
  primary: '#b17d7f',
  secondary: '#8699a7',
  tabIconDefault: '#9da6ad',
  tabTextDefault: '#a3a4a6',
  tabIconSelected: '#b17d7f',
  tabTextSelected: black,
  tabBorder: '#eaebed',
  safeArea: black,
};

export const Dark: Colors = {
  ...Light,
};

interface AppTheme extends MD3Theme {
  colors: MD3Colors & Colors;
}

export const LightTheme: AppTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...Light,
  },
};

export const DarkTheme: AppTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    ...Dark,
  },
};

export const useAppTheme = () => useTheme<AppTheme>();

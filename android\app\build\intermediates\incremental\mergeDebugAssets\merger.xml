<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-linking" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\expo-linking\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\expo-file-system\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="app.config" path="D:\Work\Devscribed\FormaPilates\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out\app.config"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\expo-modules-core\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-orientation-locker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\react-native-orientation-locker\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\expo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-community_slider" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\@react-native-community\slider\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-edge-to-edge" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\react-native-edge-to-edge\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-video" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Work\Devscribed\FormaPilates\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>
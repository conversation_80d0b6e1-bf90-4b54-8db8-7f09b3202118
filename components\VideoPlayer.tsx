import React, { useRef, useState } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Text,
  Dimensions,
  Platform,
} from 'react-native';
import Video, { OnLoadData, OnProgressData, VideoRef } from 'react-native-video';
import Orientation from 'react-native-orientation-locker';
import Slider from '@react-native-community/slider';
import { MaterialIcons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

const CustomVideoPlayer: React.FC = () => {
  const videoRef = useRef<VideoRef>(null);
  const [paused, setPaused] = useState<boolean>(true);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [showControls, setShowControls] = useState<boolean>(true);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

  const togglePlayPause = () => setPaused(!paused);

  const handleLoad = (data: OnLoadData) => {
    setDuration(data.duration);
  };

  const handleProgress = (data: OnProgressData) => {
    setCurrentTime(data.currentTime);
  };

  const handleSeek = (time: number) => {
    videoRef.current?.seek(time);
    setCurrentTime(time);
  };

  const toggleFullscreen = () => {
    if (isFullscreen) {
      Orientation.lockToPortrait();
    } else {
      Orientation.lockToLandscapeLeft();
    }
    setIsFullscreen(!isFullscreen);
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <View style={[styles.container, isFullscreen && styles.fullscreenContainer]}>
      <TouchableOpacity onPress={() => setShowControls(!showControls)} activeOpacity={1}>
        <Video
          ref={videoRef}
          source={{ uri: 'https://www.w3schools.com/html/mov_bbb.mp4' }}
          style={styles.video}
          paused={paused}
          onLoad={handleLoad}
          onProgress={handleProgress}
          resizeMode="contain"
        />
      </TouchableOpacity>

      {showControls && (
        <View style={styles.controls}>
          <View style={styles.topControls}>
            <TouchableOpacity onPress={togglePlayPause}>
              <MaterialIcons name={paused ? 'play-arrow' : 'pause'} size={30} color="#FFF" />
            </TouchableOpacity>

            <Text style={styles.time}>
              {formatTime(currentTime)} / {formatTime(duration)}
            </Text>

            <TouchableOpacity onPress={toggleFullscreen}>
              <MaterialIcons name={isFullscreen ? 'fullscreen-exit' : 'fullscreen'} size={28} color="#FFF" />
            </TouchableOpacity>
          </View>

          <Slider
            style={styles.slider}
            minimumValue={0}
            maximumValue={duration}
            value={currentTime}
            onSlidingComplete={handleSeek}
            minimumTrackTintColor="#FFF"
            maximumTrackTintColor="#888"
            thumbTintColor="#FFF"
          />
        </View>
      )}
    </View>
  );
};

export default CustomVideoPlayer;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#000',
    height: 250,
    width: '100%',
    justifyContent: 'center',
  },
  fullscreenContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: Platform.OS === 'android' ? height : width,
    width: Platform.OS === 'android' ? width : height,
    zIndex: 999,
    backgroundColor: '#000',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  controls: {
    position: 'absolute',
    bottom: 10,
    width: '100%',
    alignItems: 'center',
  },
  topControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    width: '100%',
    paddingHorizontal: 10,
  },
  time: {
    color: '#FFF',
    fontSize: 12,
  },
  slider: {
    width: '90%',
    height: 40,
    marginTop: 10,
  },
});

{"logs": [{"outputFile": "com.anonymous.FormaPilates.app-mergeDebugResources-53:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "65,139,140,141", "startColumns": "4,4,4,4", "startOffsets": "4991,10721,10822,10934", "endColumns": "109,100,111,96", "endOffsets": "5096,10817,10929,11026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3a82f0acae6d1f7b5cb6156d60929f6\\transformed\\exoplayer-ui-2.18.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3340,3405", "endColumns": "64,65", "endOffsets": "3400,3466"}, "to": {"startLines": "130,131", "startColumns": "4,4", "startOffsets": "10120,10185", "endColumns": "64,65", "endOffsets": "10180,10246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a0bedbb6c5a9105d69926961a71cb91\\transformed\\media3-exoplayer-1.4.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8746,8822,8884,8948,9019,9099,9177,9271,9368", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "8817,8879,8943,9014,9094,9172,9266,9363,9434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "928,1031,1126,1240,1326,1426,1539,1616,1691,1782,1875,1969,2063,2163,2256,2351,2449,2540,2631,2709,2812,2910,3006,3110,3209,3310,3463,16232", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "1026,1121,1235,1321,1421,1534,1611,1686,1777,1870,1964,2058,2158,2251,2346,2444,2535,2626,2704,2807,2905,3001,3105,3204,3305,3458,3555,16307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,67,68,138,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,210,211,212", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "767,3560,3637,3710,3797,3885,4691,4790,4909,5174,5233,10629,11031,11099,11159,11246,11310,11372,11436,11504,11569,11623,11732,11790,11852,11906,11981,12611,12693,12770,12860,12944,13024,13158,13236,13316,13439,13527,13605,13659,13710,13776,13844,13918,13989,14065,14136,14214,14284,14354,14454,14543,14621,14709,14799,14871,14943,15027,15078,15156,15222,15303,15386,15448,15512,15575,15644,15744,15848,15941,16041,16099,16154,16312,16396,16474", "endLines": "22,50,51,52,53,54,62,63,64,67,68,138,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,210,211,212", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "923,3632,3705,3792,3880,3960,4785,4904,4986,5228,5292,10716,11094,11154,11241,11305,11367,11431,11499,11564,11618,11727,11785,11847,11901,11976,12096,12688,12765,12855,12939,13019,13153,13231,13311,13434,13522,13600,13654,13705,13771,13839,13913,13984,14060,14131,14209,14279,14349,14449,14538,14616,14704,14794,14866,14938,15022,15073,15151,15217,15298,15381,15443,15507,15570,15639,15739,15843,15936,16036,16094,16149,16227,16391,16469,16541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "55,56,57,58,59,60,61,213", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3965,4059,4161,4258,4357,4465,4571,16546", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "4054,4156,4253,4352,4460,4566,4686,16642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2a947f51900954c22e7e3a8ad49c5aaa\\transformed\\media3-ui-1.4.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,672,758,843,921,1007,1095,1170,1234,1327,1418,1491,1558,1624,1694,1803,1913,2020,2093,2176,2252,2325,2428,2530,2594,2659,2712,2770,2818,2879,2949,3017,3083,3153,3217,3276,3340,3392,3452,3526,3600,3653", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,51,59,73,73,52,64", "endOffsets": "280,477,667,753,838,916,1002,1090,1165,1229,1322,1413,1486,1553,1619,1689,1798,1908,2015,2088,2171,2247,2320,2423,2525,2589,2654,2707,2765,2813,2874,2944,3012,3078,3148,3212,3271,3335,3387,3447,3521,3595,3648,3713"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,577,6759,6845,6930,7008,7094,7182,7257,7321,7414,7505,7578,7645,7711,7781,7890,8000,8107,8180,8263,8339,8412,8515,8617,8681,9439,9492,9550,9598,9659,9729,9797,9863,9933,9997,10056,10251,10303,10363,10437,10511,10564", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,132,133,134,135,136,137", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,51,59,73,73,52,64", "endOffsets": "375,572,762,6840,6925,7003,7089,7177,7252,7316,7409,7500,7573,7640,7706,7776,7885,7995,8102,8175,8258,8334,8407,8510,8612,8676,8741,9487,9545,9593,9654,9724,9792,9858,9928,9992,10051,10115,10298,10358,10432,10506,10559,10624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b89802f69f7f3d2fa65274bf0a5fd9f\\transformed\\media3-session-1.4.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,225,290,387,486,563,647,726,818,904,969,1074,1161,1257,1331,1420,1498,1590,1666,1736,1815,1898,1996", "endColumns": "72,96,64,96,98,76,83,78,91,85,64,104,86,95,73,88,77,91,75,69,78,82,97,103", "endOffsets": "123,220,285,382,481,558,642,721,813,899,964,1069,1156,1252,1326,1415,1493,1585,1661,1731,1810,1893,1991,2095"}, "to": {"startLines": "66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5101,5297,5394,5459,5556,5655,5732,5816,5895,5987,6073,6138,6243,6330,6426,6500,6589,6667,12101,12177,12247,12326,12409,12507", "endColumns": "72,96,64,96,98,76,83,78,91,85,64,104,86,95,73,88,77,91,75,69,78,82,97,103", "endOffsets": "5169,5389,5454,5551,5650,5727,5811,5890,5982,6068,6133,6238,6325,6421,6495,6584,6662,6754,12172,12242,12321,12404,12502,12606"}}]}]}
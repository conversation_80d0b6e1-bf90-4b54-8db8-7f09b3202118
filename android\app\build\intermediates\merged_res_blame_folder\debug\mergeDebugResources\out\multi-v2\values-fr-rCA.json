{"logs": [{"outputFile": "com.anonymous.FormaPilates.app-mergeDebugResources-53:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "964,1075,1182,1292,1379,1485,1615,1700,1780,1871,1964,2062,2157,2257,2350,2443,2538,2629,2720,2806,2916,3027,3130,3241,3349,3456,3615,17980", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "1070,1177,1287,1374,1480,1610,1695,1775,1866,1959,2057,2152,2252,2345,2438,2533,2624,2715,2801,2911,3022,3125,3236,3344,3451,3610,3709,18062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "56,57,58,59,60,61,62,232", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4279,4377,4479,4578,4680,4784,4888,18979", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "4372,4474,4573,4675,4779,4883,4997,19075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b89802f69f7f3d2fa65274bf0a5fd9f\\transformed\\media3-session-1.4.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,243,325,429,535,624,727,827,929,1037,1109,1212,1315,1407,1480,1568,1647,1750,1818,1884,1977,2071,2175", "endColumns": "81,105,81,103,105,88,102,99,101,107,71,102,102,91,72,87,78,102,67,65,92,93,103,109", "endOffsets": "132,238,320,424,530,619,722,822,924,1032,1104,1207,1310,1402,1475,1563,1642,1745,1813,1879,1972,2066,2170,2280"}, "to": {"startLines": "68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5496,5709,5815,5897,6001,6107,6196,6299,6399,6501,6609,6681,6784,6887,6979,7052,7140,7219,13117,13185,13251,13344,13438,13542", "endColumns": "81,105,81,103,105,88,102,99,101,107,71,102,102,91,72,87,78,102,67,65,92,93,103,109", "endOffsets": "5573,5810,5892,5996,6102,6191,6294,6394,6496,6604,6676,6779,6882,6974,7047,7135,7214,7317,13180,13246,13339,13433,13537,13647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,205,275,358,425,504,585,675,767,838,926,1021,1112,1192,1272,1355,1432,1505,1593,1665,1748,1821", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "120,200,270,353,420,499,580,670,762,833,921,1016,1107,1187,1267,1350,1427,1500,1588,1660,1743,1816,1896"}, "to": {"startLines": "50,66,144,146,148,168,169,216,217,218,219,224,225,226,227,228,229,230,231,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3714,5309,11762,11912,12057,13652,13731,17639,17729,17821,17892,18312,18407,18498,18578,18658,18741,18818,18891,19080,19152,19235,19308", "endColumns": "69,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "3779,5384,11827,11990,12119,13726,13807,17724,17816,17887,17975,18402,18493,18573,18653,18736,18813,18886,18974,19147,19230,19303,19383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1150,1216,1313,1393,1455,1547,1614,1688,1749,1828,1892,1946,2062,2121,2183,2237,2319,2448,2540,2615,2710,2791,2875,3019,3098,3179,3326,3419,3498,3553,3604,3670,3749,3830,3901,3981,4053,4131,4206,4278,4389,4486,4563,4661,4759,4837,4918,5018,5075,5159,5225,5308,5395,5457,5521,5584,5660,5762,5869,5966,6072,6131,6186,6275,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "278,385,493,575,676,773,873,995,1080,1145,1211,1308,1388,1450,1542,1609,1683,1744,1823,1887,1941,2057,2116,2178,2232,2314,2443,2535,2610,2705,2786,2870,3014,3093,3174,3321,3414,3493,3548,3599,3665,3744,3825,3896,3976,4048,4126,4201,4273,4384,4481,4558,4656,4754,4832,4913,5013,5070,5154,5220,5303,5390,5452,5516,5579,5655,5757,5864,5961,6067,6126,6181,6270,6357,6434,6515"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,69,70,140,145,147,149,150,151,152,153,154,155,156,157,158,159,160,161,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,3784,3891,3999,4081,4182,5002,5102,5224,5578,5643,11339,11832,11995,12124,12216,12283,12357,12418,12497,12561,12615,12731,12790,12852,12906,12988,13812,13904,13979,14074,14155,14239,14383,14462,14543,14690,14783,14862,14917,14968,15034,15113,15194,15265,15345,15417,15495,15570,15642,15753,15850,15927,16025,16123,16201,16282,16382,16439,16523,16589,16672,16759,16821,16885,16948,17024,17126,17233,17330,17436,17495,17550,18067,18154,18231", "endLines": "22,51,52,53,54,55,63,64,65,69,70,140,145,147,149,150,151,152,153,154,155,156,157,158,159,160,161,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,221,222,223", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "959,3886,3994,4076,4177,4274,5097,5219,5304,5638,5704,11431,11907,12052,12211,12278,12352,12413,12492,12556,12610,12726,12785,12847,12901,12983,13112,13899,13974,14069,14150,14234,14378,14457,14538,14685,14778,14857,14912,14963,15029,15108,15189,15260,15340,15412,15490,15565,15637,15748,15845,15922,16020,16118,16196,16277,16377,16434,16518,16584,16667,16754,16816,16880,16943,17019,17121,17228,17325,17431,17490,17545,17634,18149,18226,18307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "67,141,142,143", "startColumns": "4,4,4,4", "startOffsets": "5389,11436,11538,11657", "endColumns": "106,101,118,104", "endOffsets": "5491,11533,11652,11757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2a947f51900954c22e7e3a8ad49c5aaa\\transformed\\media3-ui-1.4.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,499,686,775,866,945,1043,1140,1219,1285,1382,1479,1544,1607,1671,1743,1864,1990,2115,2190,2278,2351,2431,2530,2631,2697,2761,2814,2872,2920,2981,3048,3125,3192,3264,3322,3381,3447,3499,3564,3643,3722,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,51,64,78,78,53,65", "endOffsets": "280,494,681,770,861,940,1038,1135,1214,1280,1377,1474,1539,1602,1666,1738,1859,1985,2110,2185,2273,2346,2426,2525,2626,2692,2756,2809,2867,2915,2976,3043,3120,3187,3259,3317,3376,3442,3494,3559,3638,3717,3771,3837"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,594,7322,7411,7502,7581,7679,7776,7855,7921,8018,8115,8180,8243,8307,8379,8500,8626,8751,8826,8914,8987,9067,9166,9267,9333,10127,10180,10238,10286,10347,10414,10491,10558,10630,10688,10747,10944,10996,11061,11140,11219,11273", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,134,135,136,137,138,139", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,51,64,78,78,53,65", "endOffsets": "375,589,776,7406,7497,7576,7674,7771,7850,7916,8013,8110,8175,8238,8302,8374,8495,8621,8746,8821,8909,8982,9062,9161,9262,9328,9392,10175,10233,10281,10342,10409,10486,10553,10625,10683,10742,10808,10991,11056,11135,11214,11268,11334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a0bedbb6c5a9105d69926961a71cb91\\transformed\\media3-exoplayer-1.4.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,272,344,427,503,600,693", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "124,189,267,339,422,498,595,688,780"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9397,9471,9536,9614,9686,9769,9845,9942,10035", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "9466,9531,9609,9681,9764,9840,9937,10030,10122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3a82f0acae6d1f7b5cb6156d60929f6\\transformed\\exoplayer-ui-2.18.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3447,3512", "endColumns": "64,65", "endOffsets": "3507,3573"}, "to": {"startLines": "132,133", "startColumns": "4,4", "startOffsets": "10813,10878", "endColumns": "64,65", "endOffsets": "10873,10939"}}]}]}
{"logs": [{"outputFile": "com.anonymous.FormaPilates.app-mergeDebugResources-53:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,278,361,428,507,589,679,771,842,929,1004,1091,1171,1251,1326,1403,1476,1567,1646,1727,1799", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "120,203,273,356,423,502,584,674,766,837,924,999,1086,1166,1246,1321,1398,1471,1562,1641,1722,1794,1874"}, "to": {"startLines": "50,66,144,146,148,168,169,216,217,218,219,224,225,226,227,228,229,230,231,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3712,5270,11716,11869,12018,13591,13670,17541,17631,17723,17794,18213,18288,18375,18455,18535,18610,18687,18760,18952,19031,19112,19184", "endColumns": "69,82,69,82,66,78,81,89,91,70,86,74,86,79,79,74,76,72,90,78,80,71,79", "endOffsets": "3777,5348,11781,11947,12080,13665,13747,17626,17718,17789,17876,18283,18370,18450,18530,18605,18682,18755,18846,19026,19107,19179,19259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b89802f69f7f3d2fa65274bf0a5fd9f\\transformed\\media3-session-1.4.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,243,325,429,535,630,730,830,935,1046,1118,1223,1329,1421,1494,1582,1661,1760,1828,1897,1982,2059,2157", "endColumns": "81,105,81,103,105,94,99,99,104,110,71,104,105,91,72,87,78,98,67,68,84,76,97,103", "endOffsets": "132,238,320,424,530,625,725,825,930,1041,1113,1218,1324,1416,1489,1577,1656,1755,1823,1892,1977,2054,2152,2256"}, "to": {"startLines": "68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5460,5673,5779,5861,5965,6071,6166,6266,6366,6471,6582,6654,6759,6865,6957,7030,7118,7197,13090,13158,13227,13312,13389,13487", "endColumns": "81,105,81,103,105,94,99,99,104,110,71,104,105,91,72,87,78,98,67,68,84,76,97,103", "endOffsets": "5537,5774,5856,5960,6066,6161,6261,6361,6466,6577,6649,6754,6860,6952,7025,7113,7192,7291,13153,13222,13307,13384,13482,13586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,1081,1196,1306,1388,1494,1624,1702,1778,1869,1962,2060,2155,2255,2348,2441,2536,2627,2718,2804,2914,3025,3128,3239,3347,3454,3613,17881", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "1076,1191,1301,1383,1489,1619,1697,1773,1864,1957,2055,2150,2250,2343,2436,2531,2622,2713,2799,2909,3020,3123,3234,3342,3449,3608,3707,17963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "56,57,58,59,60,61,62,232", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4230,4328,4430,4529,4631,4735,4839,18851", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "4323,4425,4524,4626,4730,4834,4952,18947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "67,141,142,143", "startColumns": "4,4,4,4", "startOffsets": "5353,11390,11492,11611", "endColumns": "106,101,118,104", "endOffsets": "5455,11487,11606,11711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2a947f51900954c22e7e3a8ad49c5aaa\\transformed\\media3-ui-1.4.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,692,781,872,951,1049,1146,1225,1291,1397,1504,1569,1635,1699,1771,1891,2014,2136,2211,2299,2372,2452,2543,2636,2702,2766,2819,2879,2927,2988,3059,3130,3197,3275,3340,3399,3465,3517,3577,3651,3725,3779", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,51,59,73,73,53,65", "endOffsets": "286,478,687,776,867,946,1044,1141,1220,1286,1392,1499,1564,1630,1694,1766,1886,2009,2131,2206,2294,2367,2447,2538,2631,2697,2761,2814,2874,2922,2983,3054,3125,3192,3270,3335,3394,3460,3512,3572,3646,3720,3774,3840"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,386,578,7296,7385,7476,7555,7653,7750,7829,7895,8001,8108,8173,8239,8303,8375,8495,8618,8740,8815,8903,8976,9056,9147,9240,9306,10083,10136,10196,10244,10305,10376,10447,10514,10592,10657,10716,10913,10965,11025,11099,11173,11227", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,134,135,136,137,138,139", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,51,59,73,73,53,65", "endOffsets": "381,573,782,7380,7471,7550,7648,7745,7824,7890,7996,8103,8168,8234,8298,8370,8490,8613,8735,8810,8898,8971,9051,9142,9235,9301,9365,10131,10191,10239,10300,10371,10442,10509,10587,10652,10711,10777,10960,11020,11094,11168,11222,11288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a0bedbb6c5a9105d69926961a71cb91\\transformed\\media3-exoplayer-1.4.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9370,9444,9509,9578,9650,9733,9810,9907,10000", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "9439,9504,9573,9645,9728,9805,9902,9995,10078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,69,70,140,145,147,149,150,151,152,153,154,155,156,157,158,159,160,161,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,221,222,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3782,3862,3943,4026,4135,4957,5055,5185,5542,5607,11293,11786,11952,12085,12187,12252,12327,12383,12462,12522,12576,12698,12757,12819,12873,12955,13752,13844,13919,14014,14095,14179,14323,14402,14483,14624,14717,14796,14851,14902,14968,15048,15129,15200,15280,15353,15431,15504,15576,15688,15781,15853,15945,16037,16111,16195,16287,16344,16428,16494,16577,16664,16726,16790,16853,16931,17033,17137,17234,17338,17397,17452,17968,18055,18132", "endLines": "22,51,52,53,54,55,63,64,65,69,70,140,145,147,149,150,151,152,153,154,155,156,157,158,159,160,161,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,221,222,223", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "965,3857,3938,4021,4130,4225,5050,5180,5265,5602,5668,11385,11864,12013,12182,12247,12322,12378,12457,12517,12571,12693,12752,12814,12868,12950,13085,13839,13914,14009,14090,14174,14318,14397,14478,14619,14712,14791,14846,14897,14963,15043,15124,15195,15275,15348,15426,15499,15571,15683,15776,15848,15940,16032,16106,16190,16282,16339,16423,16489,16572,16659,16721,16785,16848,16926,17028,17132,17229,17333,17392,17447,17536,18050,18127,18208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3a82f0acae6d1f7b5cb6156d60929f6\\transformed\\exoplayer-ui-2.18.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3465,3530", "endColumns": "64,65", "endOffsets": "3525,3591"}, "to": {"startLines": "132,133", "startColumns": "4,4", "startOffsets": "10782,10847", "endColumns": "64,65", "endOffsets": "10842,10908"}}]}]}
{"name": "formapilates", "main": "expo-router/entry", "license": "0BSD", "version": "1.0.0", "type": "module", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "eslint .", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\""}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-community/slider": "^4.5.7", "@react-navigation/native": "^7.1.6", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "expo": "~53.0.9", "expo-font": "~13.3.1", "expo-linking": "~7.1.5", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-orientation-locker": "^1.7.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-video": "^6.14.0", "react-native-web": "~0.20.0", "react-redux": "^9.2.0", "redux": "^5.0.1", "expo-av": "~15.1.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-native": "^5.0.0", "globals": "^16.1.0", "jest": "^29.2.1", "jest-expo": "~53.0.5", "prettier": "^3.5.3", "react-test-renderer": "19.0.0", "typescript": "~5.8.3", "@react-native-community/cli": "latest"}, "private": true}
{"name": "formapilates", "main": "index.js", "license": "0BSD", "version": "1.0.0", "scripts": {"start": "react-native start", "android": "react-native run-android", "ios": "react-native run-ios", "test": "jest --watchAll", "lint": "eslint .", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\""}, "jest": {"preset": "react-native"}, "dependencies": {"@react-native-community/slider": "^4.5.7", "@react-navigation/bottom-tabs": "^7.1.6", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.1.6", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-orientation-locker": "^1.7.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.14.0", "react-redux": "^9.2.0", "redux": "^5.0.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.2", "@babel/runtime": "^7.25.2", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@react-native-community/cli": "latest", "@react-native/babel-preset": "^0.79.2", "@react-native/metro-config": "^0.79.2", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "babel-jest": "^29.2.1", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-native": "^5.0.0", "globals": "^16.1.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "^0.77.0", "prettier": "^3.5.3", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}
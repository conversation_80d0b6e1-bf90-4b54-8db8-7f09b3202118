{"root": "D:\\Work\\Devscribed\\FormaPilates", "reactNativePath": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native", "dependencies": {"@react-native-community/slider": {"root": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\@react-native-community\\slider", "name": "@react-native-community/slider", "platforms": {"android": {"sourceDir": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\@react-native-community\\slider\\android", "packageImportPath": "import com.reactnativecommunity.slider.ReactSliderPackage;", "packageInstance": "new ReactSliderPackage()", "buildTypes": [], "libraryName": "RNCSlider", "componentDescriptors": ["RNCSliderComponentDescriptor"], "cmakeListsPath": "D:/Work/Devscribed/FormaPilates/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "D:/Work/Devscribed/FormaPilates/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-orientation-locker": {"root": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-orientation-locker", "name": "react-native-orientation-locker", "platforms": {"android": {"sourceDir": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-orientation-locker\\android", "packageImportPath": "import org.wonday.orientation.OrientationPackage;", "packageInstance": "new OrientationPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "D:/Work/Devscribed/FormaPilates/node_modules/react-native-orientation-locker/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "D:/Work/Devscribed/FormaPilates/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "D:/Work/Devscribed/FormaPilates/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "D:/Work/Devscribed/FormaPilates/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-video": {"root": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-video", "name": "react-native-video", "platforms": {"android": {"sourceDir": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-video\\android", "packageImportPath": "import com.brentvatne.react.ReactVideoPackage;", "packageInstance": "new ReactVideoPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "D:/Work/Devscribed/FormaPilates/node_modules/react-native-video/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "D:\\Work\\Devscribed\\FormaPilates\\node_modules\\react-native-edge-to-edge\\android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "D:/Work/Devscribed/FormaPilates/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.anonymous.FormaPilates", "sourceDir": "D:\\Work\\Devscribed\\FormaPilates\\android"}}}
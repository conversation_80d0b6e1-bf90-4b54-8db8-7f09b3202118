{"logs": [{"outputFile": "com.anonymous.FormaPilates.app-mergeDebugResources-53:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "56,57,58,59,60,61,62,234", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4122,4224,4327,4432,4537,4636,4740,18383", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "4219,4322,4427,4532,4631,4735,4849,18479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2a947f51900954c22e7e3a8ad49c5aaa\\transformed\\media3-ui-1.4.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,488,666,750,833,914,1007,1099,1162,1224,1313,1404,1475,1545,1606,1672,1811,1953,2090,2161,2240,2310,2375,2465,2554,2621,2689,2742,2800,2847,2908,2968,3035,3096,3161,3220,3285,3354,3408,3465,3536,3607,3659", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,138,141,136,70,78,69,64,89,88,66,67,52,57,46,60,59,66,60,64,58,64,68,53,56,70,70,51,65", "endOffsets": "282,483,661,745,828,909,1002,1094,1157,1219,1308,1399,1470,1540,1601,1667,1806,1948,2085,2156,2235,2305,2370,2460,2549,2616,2684,2737,2795,2842,2903,2963,3030,3091,3156,3215,3280,3349,3403,3460,3531,3602,3654,3720"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,583,7012,7096,7179,7260,7353,7445,7508,7570,7659,7750,7821,7891,7952,8018,8157,8299,8436,8507,8586,8656,8721,8811,8900,8967,9690,9743,9801,9848,9909,9969,10036,10097,10162,10221,10286,10485,10539,10596,10667,10738,10790", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,134,135,136,137,138,139", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,138,141,136,70,78,69,64,89,88,66,67,52,57,46,60,59,66,60,64,58,64,68,53,56,70,70,51,65", "endOffsets": "377,578,756,7091,7174,7255,7348,7440,7503,7565,7654,7745,7816,7886,7947,8013,8152,8294,8431,8502,8581,8651,8716,8806,8895,8962,9030,9738,9796,9843,9904,9964,10031,10092,10157,10216,10281,10350,10534,10591,10662,10733,10785,10851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a0bedbb6c5a9105d69926961a71cb91\\transformed\\media3-exoplayer-1.4.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,191,254,320,396,465,554,640", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "126,186,249,315,391,460,549,635,705"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9035,9111,9171,9234,9300,9376,9445,9534,9620", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "9106,9166,9229,9295,9371,9440,9529,9615,9685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "930,1046,1153,1260,1343,1448,1564,1654,1740,1831,1924,2018,2112,2212,2305,2400,2494,2585,2676,2760,2869,2973,3071,3181,3281,3388,3547,17400", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "1041,1148,1255,1338,1443,1559,1649,1735,1826,1919,2013,2107,2207,2300,2395,2489,2580,2671,2755,2864,2968,3066,3176,3276,3383,3542,3641,17477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b89802f69f7f3d2fa65274bf0a5fd9f\\transformed\\media3-session-1.4.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,224,292,386,484,567,648,737,838,914,981,1082,1161,1256,1342,1437,1513,1598,1674,1747,1829,1915,2008", "endColumns": "73,94,67,93,97,82,80,88,100,75,66,100,78,94,85,94,75,84,75,72,81,85,92,96", "endOffsets": "124,219,287,381,479,562,643,732,833,909,976,1077,1156,1251,1337,1432,1508,1593,1669,1742,1824,1910,2003,2100"}, "to": {"startLines": "68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,163,164,165,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5344,5543,5638,5706,5800,5898,5981,6062,6151,6252,6328,6395,6496,6575,6670,6756,6851,6927,12651,12727,12800,12882,12968,13061", "endColumns": "73,94,67,93,97,82,80,88,100,75,66,100,78,94,85,94,75,84,75,72,81,85,92,96", "endOffsets": "5413,5633,5701,5795,5893,5976,6057,6146,6247,6323,6390,6491,6570,6665,6751,6846,6922,7007,12722,12795,12877,12963,13056,13153"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,206,279,347,430,499,567,643,722,805,891,960,1040,1129,1209,1292,1377,1456,1533,1613,1705,1778,1857,1929", "endColumns": "68,81,72,67,82,68,67,75,78,82,85,68,79,88,79,82,84,78,76,79,91,72,78,71,77", "endOffsets": "119,201,274,342,425,494,562,638,717,800,886,955,1035,1124,1204,1287,1372,1451,1528,1608,1700,1773,1852,1924,2002"}, "to": {"startLines": "50,66,144,146,147,149,169,170,171,218,219,220,221,226,227,228,229,230,231,232,233,235,236,237,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3646,5154,11273,11413,11481,11624,13158,13226,13302,17082,17165,17251,17320,17718,17807,17887,17970,18055,18134,18211,18291,18484,18557,18636,18708", "endColumns": "68,81,72,67,82,68,67,75,78,82,85,68,79,88,79,82,84,78,76,79,91,72,78,71,77", "endOffsets": "3710,5231,11341,11476,11559,11688,13221,13297,13376,17160,17246,17315,17395,17802,17882,17965,18050,18129,18206,18286,18378,18552,18631,18703,18781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "67,141,142,143", "startColumns": "4,4,4,4", "startOffsets": "5236,10945,11052,11168", "endColumns": "107,106,115,104", "endOffsets": "5339,11047,11163,11268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3a82f0acae6d1f7b5cb6156d60929f6\\transformed\\exoplayer-ui-2.18.1\\res\\values-si\\values-si.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3354,3417", "endColumns": "62,66", "endOffsets": "3412,3479"}, "to": {"startLines": "132,133", "startColumns": "4,4", "startOffsets": "10355,10418", "endColumns": "62,66", "endOffsets": "10413,10480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,345,422,500,591,676,778,893,976,1037,1101,1190,1257,1317,1411,1475,1538,1594,1664,1731,1786,1905,1962,2026,2080,2153,2275,2358,2441,2534,2620,2705,2837,2915,2995,3117,3203,3287,3347,3399,3465,3535,3608,3679,3756,3828,3905,3977,4047,4160,4253,4326,4416,4509,4583,4655,4746,4800,4880,4946,5030,5115,5177,5241,5304,5370,5475,5580,5675,5776,5840,5896,5976,6061,6136", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,76,77,90,84,101,114,82,60,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79,84,74,75", "endOffsets": "264,340,417,495,586,671,773,888,971,1032,1096,1185,1252,1312,1406,1470,1533,1589,1659,1726,1781,1900,1957,2021,2075,2148,2270,2353,2436,2529,2615,2700,2832,2910,2990,3112,3198,3282,3342,3394,3460,3530,3603,3674,3751,3823,3900,3972,4042,4155,4248,4321,4411,4504,4578,4650,4741,4795,4875,4941,5025,5110,5172,5236,5299,5365,5470,5575,5670,5771,5835,5891,5971,6056,6131,6207"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,69,70,140,145,148,150,151,152,153,154,155,156,157,158,159,160,161,162,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,223,224,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "761,3715,3791,3868,3946,4037,4854,4956,5071,5418,5479,10856,11346,11564,11693,11787,11851,11914,11970,12040,12107,12162,12281,12338,12402,12456,12529,13381,13464,13547,13640,13726,13811,13943,14021,14101,14223,14309,14393,14453,14505,14571,14641,14714,14785,14862,14934,15011,15083,15153,15266,15359,15432,15522,15615,15689,15761,15852,15906,15986,16052,16136,16221,16283,16347,16410,16476,16581,16686,16781,16882,16946,17002,17482,17567,17642", "endLines": "22,51,52,53,54,55,63,64,65,69,70,140,145,148,150,151,152,153,154,155,156,157,158,159,160,161,162,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,223,224,225", "endColumns": "12,75,76,77,90,84,101,114,82,60,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79,84,74,75", "endOffsets": "925,3786,3863,3941,4032,4117,4951,5066,5149,5474,5538,10940,11408,11619,11782,11846,11909,11965,12035,12102,12157,12276,12333,12397,12451,12524,12646,13459,13542,13635,13721,13806,13938,14016,14096,14218,14304,14388,14448,14500,14566,14636,14709,14780,14857,14929,15006,15078,15148,15261,15354,15427,15517,15610,15684,15756,15847,15901,15981,16047,16131,16216,16278,16342,16405,16471,16576,16681,16776,16877,16941,16997,17077,17562,17637,17713"}}]}]}
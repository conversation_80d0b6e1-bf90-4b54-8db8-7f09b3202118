<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6077d64f-1bbd-46fa-aa6e-d48ede55c196" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/app.json" beforeDir="false" afterPath="$PROJECT_DIR$/app.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/(tabs)/_layout.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/(tabs)/_layout.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/(tabs)/index.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/(tabs)/two.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/+html.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/+html.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/+not-found.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/+not-found.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/_layout.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/_layout.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modal.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/modal.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/EditScreenInfo.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/ExternalLink.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/components/ExternalLink.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/Themed.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/components/Themed.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/__tests__/StyledText-test.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/useClientOnlyValue.ts" beforeDir="false" afterPath="$PROJECT_DIR$/components/useClientOnlyValue.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/useClientOnlyValue.web.ts" beforeDir="false" afterPath="$PROJECT_DIR$/components/useClientOnlyValue.web.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/useColorScheme.web.ts" beforeDir="false" afterPath="$PROJECT_DIR$/components/useColorScheme.web.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/constants/Colors.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tsconfig.json" beforeDir="false" afterPath="$PROJECT_DIR$/tsconfig.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectId" id="2xd0g8LhafUFuCMM44BINHpCoWv" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "cidr.known.project.marker": "true",
    "com.android.tools.idea.devicemanager.tab": "Physical",
    "last_opened_file_path": "D:/Work/Devscribed/FormaPilates"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6077d64f-1bbd-46fa-aa6e-d48ede55c196" name="Changes" comment="" />
      <created>1748252752113</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748252752113</updated>
    </task>
    <servers />
  </component>
</project>
import { ScrollView, StyleSheet } from 'react-native';
import { Text, View } from '@/components/Themed';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import VideoPlayer from '@/components/VideoPlayer';

export default function ExploreScreen() {
  return (
    <SafeAreaProvider>
      <SafeAreaView style={{ flex: 1, backgroundColor: 'red'}} edges={['top']} >
        <View style={styles.container}>
          <VideoPlayer />
          <ScrollView
            style={{
              width: '100%'
            }}
            horizontal={false}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
          >
              <View 
                style={{
                  backgroundColor: 'green',
                  width: '100%',
                  height: 200,
                }}
              >
                <Text>1</Text>
              </View>
              <View 
                style={{
                  backgroundColor: 'red',
                  width: 200,
                  height: 200,
                }}
              >
                <Text>2</Text>
              </View>
              <View 
                style={{
                  backgroundColor: 'green',
                  width: '100%',
                  height: 200,
                }}
              >
                <Text>3</Text>
              </View>
                <View 
                style={{
                  backgroundColor: 'red',
                  width: 200,
                  height: 200,
                }}
              >
                <Text>3</Text>
              </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'blue'
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  separator: {
    marginVertical: 30,
    height: 1,
    width: '80%',
  },
});


import { defineConfig } from 'eslint/config';
import globals from 'globals';
import react from 'eslint-plugin-react';
import reactNative from 'eslint-plugin-react-native';
import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';

const compat = new FlatCompat({
  baseDirectory: import.meta.dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default defineConfig([
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...reactNative.environments['react-native']['react-native'],
      },
      ecmaVersion: 12,
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },

    extends: compat.extends('eslint:recommended', 'plugin:react/recommended'),

    plugins: {
      react,
      'react-native': reactNative,
    },

    settings: {
      react: {
        version: 'detect',
      },
    },

    rules: {},
  },
]);

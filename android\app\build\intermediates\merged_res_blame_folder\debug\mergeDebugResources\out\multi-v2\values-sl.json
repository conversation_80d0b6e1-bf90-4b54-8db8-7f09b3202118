{"logs": [{"outputFile": "com.anonymous.FormaPilates.app-mergeDebugResources-53:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,212,283,352,433,504,571,641,724,807,889,961,1035,1117,1194,1276,1358,1434,1512,1589,1673,1747,1829,1901", "endColumns": "72,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "123,207,278,347,428,499,566,636,719,802,884,956,1030,1112,1189,1271,1353,1429,1507,1584,1668,1742,1824,1896,1978"}, "to": {"startLines": "56,72,150,152,153,155,175,176,177,224,225,226,227,232,233,234,235,236,237,238,239,241,242,243,244", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4060,5627,11979,12126,12195,12339,13898,13965,14035,17843,17926,18008,18080,18479,18561,18638,18720,18802,18878,18956,19033,19218,19292,19374,19446", "endColumns": "72,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "4128,5706,12045,12190,12271,12405,13960,14030,14113,17921,18003,18075,18149,18556,18633,18715,18797,18873,18951,19028,19112,19287,19369,19441,19523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2a947f51900954c22e7e3a8ad49c5aaa\\transformed\\media3-ui-1.4.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,958,1045,1133,1216,1314,1415,1498,1563,1660,1754,1825,1895,1959,2027,2149,2277,2399,2476,2556,2629,2709,2816,2924,2992,3057,3110,3168,3216,3277,3347,3416,3479,3544,3607,3664,3740,3792,3855,3932,4009,4063", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,121,127,121,76,79,72,79,106,107,67,64,52,57,47,60,69,68,62,64,62,56,75,51,62,76,76,53,65", "endOffsets": "318,640,953,1040,1128,1211,1309,1410,1493,1558,1655,1749,1820,1890,1954,2022,2144,2272,2394,2471,2551,2624,2704,2811,2919,2987,3052,3105,3163,3211,3272,3342,3411,3474,3539,3602,3659,3735,3787,3850,3927,4004,4058,4124"}, "to": {"startLines": "2,11,17,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,127,128,129,130,131,132,133,134,135,136,137,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,418,740,7577,7664,7752,7835,7933,8034,8117,8182,8279,8373,8444,8514,8578,8646,8768,8896,9018,9095,9175,9248,9328,9435,9543,9611,10352,10405,10463,10511,10572,10642,10711,10774,10839,10902,10959,11178,11230,11293,11370,11447,11501", "endLines": "10,16,22,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,127,128,129,130,131,132,133,134,135,136,137,140,141,142,143,144,145", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,121,127,121,76,79,72,79,106,107,67,64,52,57,47,60,69,68,62,64,62,56,75,51,62,76,76,53,65", "endOffsets": "413,735,1048,7659,7747,7830,7928,8029,8112,8177,8274,8368,8439,8509,8573,8641,8763,8891,9013,9090,9170,9243,9323,9430,9538,9606,9671,10400,10458,10506,10567,10637,10706,10769,10834,10897,10954,11030,11225,11288,11365,11442,11496,11562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9fdcbb7eb51ef8e1bfa08105cbda6005\\transformed\\browser-1.6.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "73,147,148,149", "startColumns": "4,4,4,4", "startOffsets": "5711,11661,11765,11877", "endColumns": "105,103,111,101", "endOffsets": "5812,11760,11872,11974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\33edec337de23b6d7afccb07bf9c5a56\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1320,1432,1534,1642,1729,1832,1951,2032,2110,2202,2296,2391,2485,2580,2674,2770,2870,2962,3054,3138,3246,3354,3454,3567,3675,3780,3960,18154", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "1427,1529,1637,1724,1827,1946,2027,2105,2197,2291,2386,2480,2575,2669,2765,2865,2957,3049,3133,3241,3349,3449,3562,3670,3775,3955,4055,18233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b89802f69f7f3d2fa65274bf0a5fd9f\\transformed\\media3-session-1.4.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,249,327,422,514,610,692,783,883,965,1043,1146,1237,1331,1407,1502,1582,1685,1764,1835,1916,2001,2100", "endColumns": "78,114,77,94,91,95,81,90,99,81,77,102,90,93,75,94,79,102,78,70,80,84,98,101", "endOffsets": "129,244,322,417,509,605,687,778,878,960,1038,1141,1232,1326,1402,1497,1577,1680,1759,1830,1911,1996,2095,2197"}, "to": {"startLines": "74,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,169,170,171,172,173,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5817,6026,6141,6219,6314,6406,6502,6584,6675,6775,6857,6935,7038,7129,7223,7299,7394,7474,13381,13460,13531,13612,13697,13796", "endColumns": "78,114,77,94,91,95,81,90,99,81,77,102,90,93,75,94,79,102,78,70,80,84,98,101", "endOffsets": "5891,6136,6214,6309,6401,6497,6579,6670,6770,6852,6930,7033,7124,7218,7294,7389,7469,7572,13455,13526,13607,13692,13791,13893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00054a46db3025f014b8174c079d22f2\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "62,63,64,65,66,67,68,240", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4588,4685,4787,4885,4989,5092,5194,19117", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "4680,4782,4880,4984,5087,5189,5306,19213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f50bf460fa354c8e12420350dfccc25c\\transformed\\material-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1202,1268,1362,1438,1501,1613,1673,1738,1792,1862,1922,1978,2090,2147,2209,2265,2338,2472,2557,2634,2723,2804,2889,3032,3116,3199,3333,3422,3499,3555,3610,3676,3749,3826,3897,3976,4050,4126,4201,4274,4379,4467,4540,4630,4721,4793,4867,4958,5010,5092,5159,5243,5330,5392,5456,5519,5588,5691,5799,5897,6001,6061,6120,6197,6284,6360", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1197,1263,1357,1433,1496,1608,1668,1733,1787,1857,1917,1973,2085,2142,2204,2260,2333,2467,2552,2629,2718,2799,2884,3027,3111,3194,3328,3417,3494,3550,3605,3671,3744,3821,3892,3971,4045,4121,4196,4269,4374,4462,4535,4625,4716,4788,4862,4953,5005,5087,5154,5238,5325,5387,5451,5514,5583,5686,5794,5892,5996,6056,6115,6192,6279,6355,6433"}, "to": {"startLines": "23,57,58,59,60,61,69,70,71,75,76,146,151,154,156,157,158,159,160,161,162,163,164,165,166,167,168,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1053,4133,4222,4311,4399,4497,5311,5417,5543,5896,5960,11567,12050,12276,12410,12522,12582,12647,12701,12771,12831,12887,12999,13056,13118,13174,13247,14118,14203,14280,14369,14450,14535,14678,14762,14845,14979,15068,15145,15201,15256,15322,15395,15472,15543,15622,15696,15772,15847,15920,16025,16113,16186,16276,16367,16439,16513,16604,16656,16738,16805,16889,16976,17038,17102,17165,17234,17337,17445,17543,17647,17707,17766,18238,18325,18401", "endLines": "28,57,58,59,60,61,69,70,71,75,76,146,151,154,156,157,158,159,160,161,162,163,164,165,166,167,168,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,229,230,231", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "1315,4217,4306,4394,4492,4583,5412,5538,5622,5955,6021,11656,12121,12334,12517,12577,12642,12696,12766,12826,12882,12994,13051,13113,13169,13242,13376,14198,14275,14364,14445,14530,14673,14757,14840,14974,15063,15140,15196,15251,15317,15390,15467,15538,15617,15691,15767,15842,15915,16020,16108,16181,16271,16362,16434,16508,16599,16651,16733,16800,16884,16971,17033,17097,17160,17229,17332,17440,17538,17642,17702,17761,17838,18320,18396,18474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a0bedbb6c5a9105d69926961a71cb91\\transformed\\media3-exoplayer-1.4.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,183,247,311,386,467,566,657", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "118,178,242,306,381,462,561,652,726"}, "to": {"startLines": "118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9676,9744,9804,9868,9932,10007,10088,10187,10278", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "9739,9799,9863,9927,10002,10083,10182,10273,10347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3a82f0acae6d1f7b5cb6156d60929f6\\transformed\\exoplayer-ui-2.18.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "58,59", "startColumns": "4,4", "startOffsets": "3740,3809", "endColumns": "68,73", "endOffsets": "3804,3878"}, "to": {"startLines": "138,139", "startColumns": "4,4", "startOffsets": "11035,11104", "endColumns": "68,73", "endOffsets": "11099,11173"}}]}]}